import 'package:sweph/sweph.dart';

import '../user/birth_data.dart';
import 'aspect_info.dart';
import 'chart_type.dart';
import 'firdaria_data.dart';
import 'planet_position.dart';

/// 星盤資料類別
class ChartData {
  final ChartType chartType; // 修復：改為 final 不可變屬性
  final BirthData primaryPerson; // 修復：改為 final 不可變屬性
  final BirthData? secondaryPerson; // 修復：改為 final 不可變屬性
  final DateTime? specificDate; // 修復：改為 final 不可變屬性
  final DateTime? returnDate; // 返照日期（太陽返照或月亮返照的實際計算日期）
  List<PlanetPosition>? planets;
  HouseCuspData? houses;
  List<AspectInfo>? aspects;
  List<PlanetPosition>? arabicPoints; // 阿拉伯點列表
  List<FirdariaData>? firdariaData;

  ChartData({
    required this.chartType,
    required this.primaryPerson,
    this.secondaryPerson,
    this.specificDate,
    this.returnDate,
    this.planets,
    this.houses,
    this.aspects,
    this.arabicPoints,
  });

  /// 從 JSON 創建 ChartData 對象
  factory ChartData.fromJson(Map<String, dynamic> json) {
    return ChartData(
      chartType: ChartType.values.firstWhere(
        (type) => type.toString() == json['type'],
        orElse: () => ChartType.natal,
      ),
      primaryPerson: BirthData.fromJson(json['primaryPerson'] as Map<String, dynamic>),
      secondaryPerson: json['secondaryPerson'] != null
          ? BirthData.fromJson(json['secondaryPerson'] as Map<String, dynamic>)
          : null,
      specificDate: json['specificDate'] != null
          ? DateTime.parse(json['specificDate'] as String)
          : null,
      returnDate: json['returnDate'] != null
          ? DateTime.parse(json['returnDate'] as String)
          : null,
      planets: json['planets'] != null
          ? (json['planets'] as List<dynamic>)
              .map((p) => PlanetPosition.fromJson(p as Map<String, dynamic>))
              .toList()
          : null,
      houses: json['houses'] != null
          ? HouseCuspData(
              List<double>.from(json['houses']['cusps'] as List),
              List<double>.from(json['houses']['ascmc'] as List),
            )
          : null,
      aspects: json['aspects'] != null
          ? (json['aspects'] as List<dynamic>)
              .map((a) => AspectInfo.fromJson(a as Map<String, dynamic>))
              .toList()
          : null,
      arabicPoints: json['arabicPoints'] != null
          ? (json['arabicPoints'] as List<dynamic>)
              .map((p) => PlanetPosition.fromJson(p as Map<String, dynamic>))
              .toList()
          : null,
    );
  }

  /// 將 ChartData 物件轉換為 JSON
  Map<String, dynamic> toJson() {
    return {
      'type': chartType.toString(),
      'primaryPerson': primaryPerson.toJson(),
      'secondaryPerson': secondaryPerson?.toJson(),
      'specificDate': specificDate?.toIso8601String(),
      'returnDate': returnDate?.toIso8601String(),
      'planets': planets?.map((p) => p.toJson()).toList(),
      'houses': houses != null
          ? {
              'cusps': houses!.cusps,
              'ascmc': houses!.ascmc,
            }
          : null,
      'aspects': aspects?.map((a) => a.toJson()).toList(),
      'arabicPoints': arabicPoints?.map((p) => p.toJson()).toList(),
    };
  }

  /// 獲取星盤標題
  String get title {
    if (chartType.requiresTwoPersons && secondaryPerson != null) {
      return '${primaryPerson.name} 與 ${secondaryPerson!.name} 的${chartType.name}';
    }
    return '${primaryPerson.name}的${chartType.name}';
  }

  /// 獲取特定行星的位置
  PlanetPosition? getPlanetPosition(String planetName) {
    try {
      return planets?.firstWhere((p) => p.name == planetName);
    } catch (e) {
      return null;
    }
  }

  /// 獲取特定宮位的度數
  double? getHouseDegree(int houseNumber) {
    return houses?.cusps[houseNumber];
  }

  /// 獲取特定行星的相位
  List<AspectInfo>? getPlanetAspects(String planetName) {
    return aspects
        ?.where(
            (a) => a.planet1.name == planetName || a.planet2.name == planetName)
        .toList();
  }

  /// 獲取特定宮位的所有行星
  List<PlanetPosition>? getPlanetsInHouse(int houseNumber) {
    return planets?.where((p) => p.house == houseNumber).toList();
  }

  /// 獲取特定星座的所有行星
  List<PlanetPosition>? getPlanetsInSign(String sign) {
    return planets?.where((p) => p.sign == sign).toList();
  }

  /// 獲取特定行星的所有相位角度
  List<int>? getPlanetAspectAngles(String planetName) {
    return aspects
        ?.where(
            (a) => a.planet1.name == planetName || a.planet2.name == planetName)
        .map((a) => a.angle)
        .toList();
  }

  /// 獲取特定宮位的所有相位
  List<AspectInfo>? getAspectsInHouse(int houseNumber) {
    return aspects
        ?.where((a) =>
            getPlanetPosition(a.planet1.name)?.house == houseNumber ||
            getPlanetPosition(a.planet2.name)?.house == houseNumber)
        .toList();
  }

  /// 獲取特定星座的所有相位
  List<AspectInfo>? getAspectsInSign(String sign) {
    return aspects
        ?.where((a) =>
            getPlanetPosition(a.planet1.name)?.sign == sign ||
            getPlanetPosition(a.planet2.name)?.sign == sign)
        .toList();
  }

  /// 獲取特定阿拉伯點的位置
  PlanetPosition? getArabicPoint(int pointId) {
    try {
      return arabicPoints?.firstWhere((p) => p.id == pointId);
    } catch (e) {
      return null;
    }
  }

  /// 獲取特定阿拉伯點的位置（根據名稱）
  PlanetPosition? getArabicPointByName(String pointName) {
    try {
      return arabicPoints?.firstWhere((p) => p.name == pointName);
    } catch (e) {
      return null;
    }
  }

  /// 創建一個新的 ChartData 實例，並更新指定的屬性
  ChartData copyWith({
    ChartType? chartType,
    BirthData? primaryPerson,
    BirthData? secondaryPerson,
    DateTime? specificDate,
    DateTime? returnDate,
    List<PlanetPosition>? planets,
    HouseCuspData? houses,
    List<AspectInfo>? aspects,
    List<PlanetPosition>? arabicPoints,
    List<FirdariaData>? firdariaData,
  }) {
    return ChartData(
      chartType: chartType ?? this.chartType,
      primaryPerson: primaryPerson ?? this.primaryPerson,
      secondaryPerson: secondaryPerson ?? this.secondaryPerson,
      specificDate: specificDate ?? this.specificDate,
      returnDate: returnDate ?? this.returnDate,
      planets: planets ?? this.planets,
      houses: houses ?? this.houses,
      aspects: aspects ?? this.aspects,
      arabicPoints: arabicPoints ?? this.arabicPoints,
    )..firdariaData = firdariaData ?? this.firdariaData;
  }
}
